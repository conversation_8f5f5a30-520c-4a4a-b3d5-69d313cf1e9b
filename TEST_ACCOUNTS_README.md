# Test Email Accounts for IMAP Provider Testing

Этот набор файлов содержит тестовые email аккаунты для проверки функциональности автодетекции IMAP провайдеров в приложении.

## Файлы

### 1. `test_accounts.txt`
**Основной тестовый файл** с ~300 аккаунтами в формате `email:password`

**Покрытие провайдеров:**
- **Gmail** - gmail.com, googlemail.com
- **Outlook/Hotmail** - outlook.*, hotmail.* (включая международные домены)
- **Yahoo** - yahoo.com, ymail.com, rocketmail.com
- **iCloud** - icloud.com, me.com, mac.com
- **GMX** - gmx.*, caramail.*
- **AOL** - aol.com
- **Zoho** - zoho.com
- **Mail.ru** - mail.ru, bk.ru, list.ru, inbox.ru, bizml.ru
- **Yandex** - yandex.*, ya.ru, narod.ru
- **Rambler** - rambler.ru, lenta.ru, autorambler.ru, myrambler.ru, ro.ru
- **QIP.ru** - qip.ru
- **Pochta.ru** - pochta.ru
- **Fastmail** - fastmail.com, fastmail.fm
- **NGS.ru** - ngs.ru, eml.ru, mosk.ru, samara24.ru, nn.ru, prm.ru
- **Inbox.lv** - inbox.lv, inbox.lt, mail.ee
- **Mail.com** - 200+ доменов включая mail.com, email.com, usa.com и множество специализированных доменов
- **Dragons Mail** - modermail.com, faunmail.com, dragonsmail.com, gutsmail.com, smarmail.com
- **FirstMail** - firstmail.com и связанные домены

### 2. `test_accounts_alternative_formats.txt`
**Файл для тестирования различных форматов** (~200 аккаунтов)

**Поддерживаемые форматы:**
- `email:password` (основной)
- `email;password` (точка с запятой)
- `email|password` (вертикальная черта)
- `email	password` (табуляция)
- `email,password` (запятая)
- `email:password:refresh_token:client_id` (Microsoft OAuth2)

**Специальные случаи:**
- Пароли с специальными символами
- Длинные пароли
- Международные символы в паролях
- Email адреса с точками, подчеркиваниями, плюсами
- Международные доменные имена
- Поддомены
- Различные TLD (.edu, .gov, .org, .museum, .travel, .aero)

## Как использовать

### В приложении:
1. Откройте диалог импорта аккаунтов
2. Перетащите один из тестовых файлов в область загрузки
3. Проверьте предварительный просмотр
4. Запустите импорт
5. Наблюдайте за процессом автодетекции IMAP настроек

### Что проверить:

#### Автодетекция провайдеров:
- ✅ Известные провайдеры должны определяться мгновенно
- ✅ Новые домены из расширенного списка должны находить правильные IMAP серверы
- ✅ Wildcard паттерны должны работать (например, outlook.de → outlook.office365.com)
- ✅ Fallback паттерны должны срабатывать для неизвестных доменов

#### Парсинг форматов:
- ✅ Автодетекция разделителей должна работать корректно
- ✅ Специальные символы в паролях должны обрабатываться правильно
- ✅ Microsoft OAuth2 формат должен распознаваться
- ✅ Смешанные форматы в одном файле должны обрабатываться

#### Производительность:
- ✅ Импорт 300+ аккаунтов должен завершаться за разумное время
- ✅ Прогресс должен отображаться корректно
- ✅ Память не должна расти чрезмерно

#### Обработка ошибок:
- ✅ Некорректные строки должны пропускаться
- ✅ Счетчики пропущенных строк должны быть точными
- ✅ Приложение не должно зависать на проблемных данных

## Ожидаемые результаты

### Успешная автодетекция для:
- **Gmail**: imap.gmail.com:993
- **Outlook**: outlook.office365.com:993
- **Mail.ru**: imap.mail.ru:993
- **Yandex**: imap.yandex.ru:993
- **Mail.com**: imap.mail.com:993
- **GMX**: imap.gmx.com:993
- **NGS.ru**: mail.ngs.ru:993
- **Inbox.lv**: mail.inbox.lv:993
- **Dragons Mail**: pop.dragonsmail.com:993
- **FirstMail**: imap.firstmail.ltd:993

### Fallback паттерны для неизвестных доменов:
- `imap.{domain}:993`
- `mail.{domain}:993`
- `mx.{domain}:993`
- `pop.{domain}:993`

## Безопасность

⚠️ **ВАЖНО**: Все пароли в этих файлах являются тестовыми и НЕ должны использоваться для реальных аккаунтов. Email адреса также являются вымышленными для целей тестирования.

## Логирование

Во время тестирования обратите внимание на логи:
- Сообщения об успешной детекции провайдеров
- Предупреждения о fallback паттернах
- Ошибки валидации серверов
- Статистика производительности

## Расширение тестов

Для добавления новых тестовых случаев:
1. Добавьте новые домены в `test_accounts.txt`
2. Убедитесь, что соответствующий провайдер есть в `imapProviders.ts`
3. Протестируйте автодетекцию
4. Обновите этот README при необходимости
