/**
 * @file Custom scrollbar component with modern design
 */
import React, { forwardRef } from 'react';
import { cn } from '../utils/utils';

interface CustomScrollbarProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'thin' | 'account-list';
  children: React.ReactNode;
}

/**
 * Custom scrollbar component with modern styling
 * Provides different variants for different use cases
 */
export const CustomScrollbar = forwardRef<HTMLDivElement, CustomScrollbarProps>(
  ({ variant = 'default', className, children, ...props }, ref) => {
    const scrollbarClass = {
      default: 'custom-scrollbar',
      thin: 'custom-scrollbar-thin',
      'account-list': 'account-list-scrollbar'
    }[variant];

    // For account-list variant, use overlay scrolling to prevent layout shift
    const overflowStyle = variant === 'account-list' ? 'overlay' : 'auto';

    return (
      <div
        ref={ref}
        className={cn(
          scrollbarClass,
          className
        )}
        style={{
          overflow: overflowStyle,
          // Fallback for browsers that don't support overflow: overlay
          ...(variant === 'account-list' && {
            scrollbarGutter: 'stable',
          })
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CustomScrollbar.displayName = 'CustomScrollbar';

export default CustomScrollbar;
