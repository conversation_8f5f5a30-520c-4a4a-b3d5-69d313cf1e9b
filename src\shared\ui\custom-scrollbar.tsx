/**
 * @file Custom scrollbar component with modern design
 */
import React, { forwardRef } from 'react';
import { cn } from '../utils/utils';

interface CustomScrollbarProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'thin' | 'account-list';
  children: React.ReactNode;
}

/**
 * Custom scrollbar component with modern styling
 * Provides different variants for different use cases
 */
export const CustomScrollbar = forwardRef<HTMLDivElement, CustomScrollbarProps>(
  ({ variant = 'default', className, children, ...props }, ref) => {
    const scrollbarClass = {
      default: 'custom-scrollbar',
      thin: 'custom-scrollbar-thin',
      'account-list': 'account-list-scrollbar'
    }[variant];

    return (
      <div
        ref={ref}
        className={cn(
          'overflow-auto',
          scrollbarClass,
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CustomScrollbar.displayName = 'CustomScrollbar';

export default CustomScrollbar;
