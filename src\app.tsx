import React, { useEffect, useCallback, useRef } from 'react';
import { Toaster } from 'sonner';

import ErrorBoundary from './components/ErrorBoundary';
import Layout from './components/Layout';
import { useAccountInitializer } from './shared/hooks/useAccountInitializer';
import { useAccountStore } from './shared/store/accounts/accountStore';
import { useLogStore } from './shared/store/logStore';
import { ThemeProvider } from './shared/ui/theme-provider';
import { logger } from './shared/lib/logger';


const App = (): React.JSX.Element => {
  const { addLog } = useLogStore();
  const { setAccountConnectionStatus } = useAccountStore();
  const isInitialized = useRef(false);

  logger.info('App component rendering...');

  // Initialize the application (load accounts, proxy settings, etc.)
  useAccountInitializer();

  // Memoize the log handler to prevent recreation on every render
  const handleLogAdd = useCallback((log: any) => {
    // Add additional check to prevent potential loops
    if (log && typeof log === 'object' && log.msg) {
      addLog(log);
    }
  }, [addLog]);

  // Memoize the connection status handler
  const handleConnectionStatus = useCallback(({ accountId, status }: { accountId: string, status: 'connected' | 'connecting' | 'disconnected' }) => {
    setAccountConnectionStatus(accountId, status);
  }, [setAccountConnectionStatus]);

  useEffect(() => {
    // Prevent multiple initializations
    if (isInitialized.current) {
      return;
    }

    try {
      // Listen for log messages from the main process
      window.ipcApi.on('log:add', handleLogAdd);

      // Listen for account connection status updates
      window.ipcApi.on('account:connection-status', handleConnectionStatus);

      // Notify the main process that the renderer is ready
      void window.ipcApi.rendererReady();

      isInitialized.current = true;
    } catch (error) {
      logger.error(`Failed to initialize application: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [handleLogAdd, handleConnectionStatus]);

  return (
    <ThemeProvider defaultTheme="dark" storageKey="imapviewer-ui-theme">
      <ErrorBoundary>
        <Layout />
        <Toaster
          position="top-right"
          richColors
          closeButton
          toastOptions={{
            style: {
              background: 'hsl(var(--background))',
              border: '1px solid hsl(var(--border))',
              color: 'hsl(var(--foreground))',
            },
          }}
        />
      </ErrorBoundary>
    </ThemeProvider>
  );
};

export default App;